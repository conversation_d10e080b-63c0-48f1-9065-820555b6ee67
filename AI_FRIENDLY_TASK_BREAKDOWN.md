# React 重构实施指南 - AI Vibe Coding 详细任务拆分

## 🎯 总体实施策略

### 核心原则
1. **渐进式重构**: 保持现有功能完全可用
2. **最小风险**: 每个步骤都可以独立验证和回滚
3. **AI友好**: 每个任务都有明确的输入、输出和验证标准
4. **模块化开发**: 每个组件可以独立开发和测试

### 文件结构映射
```
现有结构 → React重构后
├── admin/partials/notion-to-wordpress-admin-display.php → frontend/src/App.tsx
├── assets/js/admin-interactions.js → frontend/src/components/Dashboard/
├── assets/js/sync-progress-manager.js → frontend/src/hooks/useSSE.ts
├── assets/js/sse-progress-manager.js → frontend/src/services/sse.ts
└── assets/css/admin-modern.css → frontend/src/styles/tailwind.css
```

---

## 📅 Week 1: 项目基础搭建

### Task 1.1: 环境配置 (Day 1, 4小时)

**目标**: 在现有插件中创建 React 开发环境

**输入文件**:
- 当前的 `package.json`
- WordPress 插件目录结构

**输出文件**:
```
frontend/
├── package.json
├── tsconfig.json
├── vite.config.ts
├── tailwind.config.js
├── eslint.config.js
└── src/
    ├── main.tsx
    ├── App.tsx
    └── vite-env.d.ts
```

**具体步骤**:
1. 在插件根目录创建 `frontend/` 文件夹
2. 初始化 `package.json`:
```json
{
  "name": "notion-wp-frontend",
  "version": "1.0.0",
  "type": "module",
  "scripts": {
    "dev": "vite",
    "build": "tsc && vite build",
    "preview": "vite preview"
  },
  "dependencies": {
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "zustand": "^4.4.0",
    "axios": "^1.4.0",
    "lucide-react": "^0.263.0",
    "react-hot-toast": "^2.4.0"
  },
  "devDependencies": {
    "@types/react": "^18.2.15",
    "@types/react-dom": "^18.2.7",
    "@vitejs/plugin-react": "^4.0.3",
    "typescript": "^5.0.2",
    "vite": "^4.4.5",
    "tailwindcss": "^3.3.0",
    "autoprefixer": "^10.4.14",
    "postcss": "^8.4.24",
    "eslint": "^8.45.0",
    "@typescript-eslint/eslint-plugin": "^6.0.0",
    "@typescript-eslint/parser": "^6.0.0",
    "prettier": "^3.0.0"
  }
}
```

**验证标准**:
- [ ] `npm install` 成功执行
- [ ] `npm run dev` 启动开发服务器
- [ ] TypeScript 编译无错误

### Task 1.2: WordPress 集成配置 (Day 1-2, 6小时)

**目标**: 配置 WordPress 加载 React 构建产物

**需要修改的文件**:
- `admin/class-notion-to-wordpress-admin.php`
- `includes/framework/Main.php`

**输出文件**:
- `frontend/vite.config.ts` (WordPress 集成配置)
- PHP 文件的 React 资源加载逻辑

**Vite 配置模板**:
```typescript
// frontend/vite.config.ts
import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import path from 'path';

export default defineConfig({
  plugins: [react()],
  
  build: {
    outDir: '../assets/dist',
    emptyOutDir: true,
    rollupOptions: {
      input: './src/main.tsx',
      output: {
        entryFileNames: 'js/admin-react.[hash].js',
        chunkFileNames: 'js/chunk.[hash].js',
        assetFileNames: 'css/admin-react.[hash].css',
      },
    },
  },
  
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
  
  server: {
    port: 3000,
  },
});
```

**PHP 集成代码**:
需要在 `enqueue_styles_and_scripts` 方法中添加:
```php
// 检查是否存在 React 构建文件
$react_manifest = plugin_dir_path(__FILE__) . '../assets/dist/manifest.json';
if (file_exists($react_manifest)) {
    $manifest = json_decode(file_get_contents($react_manifest), true);
    
    // 加载 React 应用
    wp_enqueue_script(
        'notion-wp-react',
        plugin_dir_url(__FILE__) . '../assets/dist/' . $manifest['src/main.tsx']['file'],
        [],
        NOTION_TO_WORDPRESS_VERSION,
        true
    );
    
    // 传递配置给 React
    wp_localize_script('notion-wp-react', 'wpNotionConfig', [
        'ajaxUrl' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('notion_wp_nonce'),
        'apiUrl' => home_url('/wp-json/notion-to-wordpress/v1/'),
    ]);
}
```

**验证标准**:
- [ ] React 开发服务器可以访问 WordPress 数据
- [ ] 生产构建文件可以正确加载
- [ ] WordPress 全局变量可以在 React 中访问

### Task 1.3: 基础组件和类型定义 (Day 2-3, 8小时)

**目标**: 创建基础的 TypeScript 类型和通用组件

**输出文件**:
```
src/
├── types/
│   ├── api.ts
│   ├── sync.ts
│   ├── settings.ts
│   └── wordpress.ts
├── components/
│   └── Common/
│       ├── Button.tsx
│       ├── Input.tsx
│       ├── Loading.tsx
│       └── ErrorBoundary.tsx
├── utils/
│   ├── constants.ts
│   └── helpers.ts
└── styles/
    ├── globals.css
    └── tailwind.css
```

**类型定义模板**:
```typescript
// src/types/api.ts
export interface ApiResponse<T = any> {
  success: boolean;
  data: T;
  message?: string;
}

export interface SyncRequest {
  type: 'smart' | 'full';
  incremental?: boolean;
  checkDeletions?: boolean;
}

export interface SyncResponse {
  taskId: string;
  message: string;
}

// src/types/sync.ts
export interface SyncStatus {
  isActive: boolean;
  progress: number;
  status: 'idle' | 'running' | 'completed' | 'error' | 'cancelled';
  currentStep: string;
  taskId: string | null;
  startTime: string | null;
  errors: string[];
  processed: number;
  total: number;
}

export interface SyncStats {
  imported_count: number;
  published_count: number;
  last_update: string;
  next_run: string | null;
}
```

**基础组件模板**:
```typescript
// src/components/Common/Button.tsx
import React from 'react';
import { cn } from '@/utils/helpers';

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'danger';
  size?: 'sm' | 'md' | 'lg';
  loading?: boolean;
  children: React.ReactNode;
}

export const Button: React.FC<ButtonProps> = ({
  variant = 'primary',
  size = 'md',
  loading = false,
  className,
  children,
  disabled,
  ...props
}) => {
  const baseClasses = 'inline-flex items-center justify-center font-medium rounded-md transition-colors';
  
  const variants = {
    primary: 'bg-blue-600 text-white hover:bg-blue-700 disabled:bg-blue-300',
    secondary: 'bg-gray-200 text-gray-900 hover:bg-gray-300 disabled:bg-gray-100',
    danger: 'bg-red-600 text-white hover:bg-red-700 disabled:bg-red-300',
  };
  
  const sizes = {
    sm: 'px-3 py-1.5 text-sm',
    md: 'px-4 py-2 text-sm',
    lg: 'px-6 py-3 text-base',
  };

  return (
    <button
      className={cn(
        baseClasses,
        variants[variant],
        sizes[size],
        loading && 'opacity-75 cursor-not-allowed',
        className
      )}
      disabled={disabled || loading}
      {...props}
    >
      {loading && (
        <svg className="w-4 h-4 mr-2 animate-spin" fill="none" viewBox="0 0 24 24">
          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" />
          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
        </svg>
      )}
      {children}
    </button>
  );
};
```

**验证标准**:
- [ ] TypeScript 类型定义完整且无错误
- [ ] 基础组件可以正常渲染
- [ ] Tailwind CSS 样式正确应用

---

## 📅 Week 2: 同步功能组件开发

### Task 2.1: 状态管理实现 (Day 1, 6小时)

**目标**: 实现 Zustand 状态管理，复用现有 API

**输出文件**:
```
src/
├── stores/
│   ├── syncStore.ts
│   ├── settingsStore.ts
│   └── uiStore.ts
└── services/
    ├── api.ts
    └── sse.ts
```

**Sync Store 实现**:
```typescript
// src/stores/syncStore.ts
import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';
import { apiService } from '@/services/api';
import type { SyncStatus, SyncStats } from '@/types/sync';

interface SyncStore {
  // 状态
  syncStatus: SyncStatus;
  stats: SyncStats;
  isLoading: boolean;
  
  // Actions
  startSync: (type: 'smart' | 'full') => Promise<void>;
  updateProgress: (progress: Partial<SyncStatus>) => void;
  updateStats: (stats: Partial<SyncStats>) => void;
  resetSync: () => void;
  fetchStats: () => Promise<void>;
}

export const useSyncStore = create<SyncStore>()(
  subscribeWithSelector((set, get) => ({
    syncStatus: {
      isActive: false,
      progress: 0,
      status: 'idle',
      currentStep: '',
      taskId: null,
      startTime: null,
      errors: [],
      processed: 0,
      total: 0,
    },
    stats: {
      imported_count: 0,
      published_count: 0,
      last_update: '',
      next_run: null,
    },
    isLoading: false,

    startSync: async (type) => {
      set({ isLoading: true });
      try {
        const response = await apiService.startSync({ type });
        if (response.success) {
          set((state) => ({
            syncStatus: {
              ...state.syncStatus,
              isActive: true,
              taskId: response.data.taskId,
              status: 'running',
              startTime: new Date().toISOString(),
            },
          }));
        }
      } catch (error) {
        console.error('Failed to start sync:', error);
      } finally {
        set({ isLoading: false });
      }
    },

    updateProgress: (progress) => {
      set((state) => ({
        syncStatus: { ...state.syncStatus, ...progress },
      }));
    },

    updateStats: (stats) => {
      set((state) => ({
        stats: { ...state.stats, ...stats },
      }));
    },

    resetSync: () => {
      set((state) => ({
        syncStatus: {
          ...state.syncStatus,
          isActive: false,
          progress: 0,
          status: 'idle',
          taskId: null,
        },
      }));
    },

    fetchStats: async () => {
      try {
        const stats = await apiService.getStats();
        set({ stats });
      } catch (error) {
        console.error('Failed to fetch stats:', error);
      }
    },
  }))
);
```

**API Service 实现**:
```typescript
// src/services/api.ts
import axios, { AxiosInstance } from 'axios';
import type { ApiResponse, SyncRequest, SyncResponse } from '@/types/api';

class ApiService {
  private client: AxiosInstance;

  constructor() {
    this.client = axios.create({
      baseURL: window.wpNotionConfig?.ajaxUrl || '/wp-admin/admin-ajax.php',
      timeout: 60000,
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
    });

    this.setupInterceptors();
  }

  private setupInterceptors(): void {
    this.client.interceptors.request.use((config) => {
      const formData = new URLSearchParams();
      
      // 添加 nonce
      formData.append('nonce', window.wpNotionConfig.nonce);
      
      // 添加其他数据
      if (config.data) {
        Object.entries(config.data).forEach(([key, value]) => {
          formData.append(key, String(value));
        });
      }
      
      config.data = formData;
      return config;
    });

    this.client.interceptors.response.use(
      (response) => {
        if (!response.data.success) {
          throw new Error(response.data.data?.message || 'Request failed');
        }
        return response;
      },
      (error) => {
        throw new Error(error.response?.data?.message || error.message);
      }
    );
  }

  async startSync(request: SyncRequest): Promise<ApiResponse<SyncResponse>> {
    const response = await this.client.post('', {
      action: 'notion_to_wordpress_manual_sync',
      sync_type: request.type,
      incremental: request.incremental ? '1' : '0',
      check_deletions: request.checkDeletions ? '1' : '0',
    });
    return response.data;
  }

  async getStats(): Promise<any> {
    const response = await this.client.post('', {
      action: 'notion_to_wordpress_get_stats',
    });
    return response.data.data;
  }

  async getSettings(): Promise<any> {
    const response = await this.client.post('', {
      action: 'notion_to_wordpress_get_settings',
    });
    return response.data.data;
  }

  async saveSettings(settings: Record<string, any>): Promise<any> {
    const response = await this.client.post('', {
      action: 'notion_to_wordpress_save_settings',
      ...settings,
    });
    return response.data.data;
  }

  async testConnection(apiKey: string, databaseId: string): Promise<boolean> {
    const response = await this.client.post('', {
      action: 'notion_to_wordpress_test_connection',
      api_key: apiKey,
      database_id: databaseId,
    });
    return response.data.data.connected;
  }
}

export const apiService = new ApiService();
```

**验证标准**:
- [ ] Zustand store 可以正确管理状态
- [ ] API 调用返回正确的数据格式
- [ ] 状态更新触发组件重新渲染

### Task 2.2: SSE Hook 实现 (Day 1-2, 4小时)

**目标**: 实现复用现有 SSE 进度推送的 React Hook

**输出文件**:
- `src/hooks/useSSE.ts`

**SSE Hook 实现**:
```typescript
// src/hooks/useSSE.ts
import { useEffect, useRef } from 'react';
import { useSyncStore } from '@/stores/syncStore';
import { toast } from 'react-hot-toast';

export const useSSE = (taskId: string | null) => {
  const eventSourceRef = useRef<EventSource | null>(null);
  const { updateProgress, updateStats, resetSync } = useSyncStore();

  useEffect(() => {
    if (!taskId) {
      return;
    }

    const params = new URLSearchParams({
      action: 'notion_to_wordpress_sse_progress',
      task_id: taskId,
      nonce: window.wpNotionConfig.nonce,
    });

    const eventSource = new EventSource(
      `${window.wpNotionConfig.ajaxUrl}?${params.toString()}`
    );
    
    eventSourceRef.current = eventSource;

    // 连接建立
    eventSource.addEventListener('connected', (event) => {
      console.log('SSE connected:', event.data);
    });

    // 进度更新
    eventSource.addEventListener('progress', (event) => {
      try {
        const data = JSON.parse(event.data);
        updateProgress({
          progress: data.percentage || 0,
          currentStep: data.current_status || data.message || '',
          processed: data.processed || 0,
          total: data.total || 0,
          status: 'running',
        });
      } catch (error) {
        console.error('Failed to parse progress data:', error);
      }
    });

    // 同步完成
    eventSource.addEventListener('completed', (event) => {
      try {
        const data = JSON.parse(event.data);
        updateProgress({
          progress: 100,
          status: 'completed',
          isActive: false,
          currentStep: '同步完成',
        });
        
        toast.success('同步已完成！');
        
        // 刷新统计数据
        setTimeout(() => {
          useSyncStore.getState().fetchStats();
        }, 1000);
      } catch (error) {
        console.error('Failed to parse completion data:', error);
      } finally {
        eventSource.close();
      }
    });

    // 同步失败
    eventSource.addEventListener('failed', (event) => {
      try {
        const data = JSON.parse(event.data);
        updateProgress({
          status: 'error',
          isActive: false,
          errors: [data.message || '同步失败'],
        });
        
        toast.error(`同步失败: ${data.message || '未知错误'}`);
      } catch (error) {
        console.error('Failed to parse error data:', error);
      } finally {
        eventSource.close();
      }
    });

    // 连接错误
    eventSource.onerror = (error) => {
      console.error('SSE connection error:', error);
      if (eventSource.readyState === EventSource.CLOSED) {
        updateProgress({
          status: 'error',
          isActive: false,
          errors: ['实时连接中断'],
        });
        toast.error('实时连接中断');
      }
    };

    // 清理函数
    return () => {
      if (eventSourceRef.current) {
        eventSourceRef.current.close();
        eventSourceRef.current = null;
      }
    };
  }, [taskId, updateProgress, updateStats, resetSync]);

  return eventSourceRef.current;
};
```

**验证标准**:
- [ ] SSE 连接可以正常建立
- [ ] 进度更新可以实时接收
- [ ] 错误处理和连接清理正常工作

### Task 2.3: 同步仪表板组件 (Day 2-3, 8小时)

**目标**: 实现同步仪表板的核心组件

**输出文件**:
```
src/components/Dashboard/
├── SyncDashboard.tsx
├── StatsCards.tsx
├── SyncButtons.tsx
├── ProgressBar.tsx
└── index.ts
```

**统计卡片组件**:
```typescript
// src/components/Dashboard/StatsCards.tsx
import React from 'react';
import { FileText, CheckCircle, Clock, Calendar } from 'lucide-react';
import type { SyncStats } from '@/types/sync';

interface StatsCardsProps {
  stats: SyncStats;
  isLoading?: boolean;
}

export const StatsCards: React.FC<StatsCardsProps> = ({ stats, isLoading = false }) => {
  const cards = [
    {
      title: '已导入页面',
      value: stats.imported_count,
      icon: FileText,
      color: 'text-blue-600 bg-blue-50',
    },
    {
      title: '已发布页面',
      value: stats.published_count,
      icon: CheckCircle,
      color: 'text-green-600 bg-green-50',
    },
    {
      title: '最后同步',
      value: stats.last_update || '从未',
      icon: Clock,
      color: 'text-yellow-600 bg-yellow-50',
    },
    {
      title: '下次同步',
      value: stats.next_run || '未计划',
      icon: Calendar,
      color: 'text-purple-600 bg-purple-50',
    },
  ];

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {[...Array(4)].map((_, index) => (
          <div key={index} className="bg-white rounded-lg shadow p-6">
            <div className="animate-pulse">
              <div className="h-8 w-8 bg-gray-200 rounded mb-3"></div>
              <div className="h-6 bg-gray-200 rounded mb-2"></div>
              <div className="h-4 bg-gray-200 rounded w-3/4"></div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      {cards.map((card, index) => {
        const Icon = card.icon;
        return (
          <div key={index} className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className={`p-2 rounded-lg ${card.color}`}>
                <Icon className="h-6 w-6" />
              </div>
              <div className="ml-4">
                <h3 className="text-2xl font-semibold text-gray-900">
                  {typeof card.value === 'number' ? card.value.toLocaleString() : card.value}
                </h3>
                <p className="text-sm text-gray-600">{card.title}</p>
              </div>
            </div>
          </div>
        );
      })}
    </div>
  );
};
```

**同步按钮组件**:
```typescript
// src/components/Dashboard/SyncButtons.tsx
import React from 'react';
import { Lightbulb, RefreshCw } from 'lucide-react';
import { Button } from '@/components/Common/Button';
import { useSyncStore } from '@/stores/syncStore';

interface SyncButtonsProps {
  disabled?: boolean;
}

export const SyncButtons: React.FC<SyncButtonsProps> = ({ disabled = false }) => {
  const { startSync, isLoading } = useSyncStore();

  const handleSmartSync = () => {
    startSync('smart');
  };

  const handleFullSync = () => {
    startSync('full');
  };

  return (
    <div className="space-y-4">
      <div className="flex flex-col sm:flex-row gap-3">
        <Button
          onClick={handleSmartSync}
          disabled={disabled}
          loading={isLoading}
          className="flex-1"
          variant="primary"
        >
          <Lightbulb className="w-4 h-4 mr-2" />
          智能同步
        </Button>
        
        <Button
          onClick={handleFullSync}
          disabled={disabled}
          loading={isLoading}
          className="flex-1"
          variant="secondary"
        >
          <RefreshCw className="w-4 h-4 mr-2" />
          完全同步
        </Button>
      </div>
      
      <div className="text-sm text-gray-600 space-y-1">
        <p><strong>智能同步</strong>: 只同步有变化的页面，速度更快</p>
        <p><strong>完全同步</strong>: 同步所有页面，确保数据一致性</p>
      </div>
    </div>
  );
};
```

**进度条组件**:
```typescript
// src/components/Dashboard/ProgressBar.tsx
import React from 'react';
import { AlertCircle, CheckCircle, Loader2 } from 'lucide-react';

interface ProgressBarProps {
  progress: number;
  status: string;
  currentStep: string;
  errors?: string[];
}

export const ProgressBar: React.FC<ProgressBarProps> = ({
  progress,
  status,
  currentStep,
  errors = [],
}) => {
  const getStatusIcon = () => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-5 h-5 text-green-600" />;
      case 'error':
        return <AlertCircle className="w-5 h-5 text-red-600" />;
      case 'running':
        return <Loader2 className="w-5 h-5 text-blue-600 animate-spin" />;
      default:
        return null;
    }
  };

  const getStatusColor = () => {
    switch (status) {
      case 'completed':
        return 'bg-green-600';
      case 'error':
        return 'bg-red-600';
      case 'running':
        return 'bg-blue-600';
      default:
        return 'bg-gray-300';
    }
  };

  return (
    <div className="space-y-3">
      {/* 进度条 */}
      <div className="relative">
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center space-x-2">
            {getStatusIcon()}
            <span className="text-sm font-medium text-gray-700">
              {currentStep || '准备中...'}
            </span>
          </div>
          <span className="text-sm font-medium text-gray-700">
            {Math.round(progress)}%
          </span>
        </div>
        
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div
            className={`h-2 rounded-full transition-all duration-300 ${getStatusColor()}`}
            style={{ width: `${Math.min(progress, 100)}%` }}
          />
        </div>
      </div>

      {/* 错误信息 */}
      {errors.length > 0 && (
        <div className="bg-red-50 border border-red-200 rounded-md p-3">
          <div className="flex">
            <AlertCircle className="w-5 h-5 text-red-400 mr-2 flex-shrink-0 mt-0.5" />
            <div className="text-sm text-red-700">
              <p className="font-medium mb-1">同步过程中出现错误:</p>
              <ul className="list-disc list-inside space-y-1">
                {errors.map((error, index) => (
                  <li key={index}>{error}</li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
```

**主仪表板组件**:
```typescript
// src/components/Dashboard/SyncDashboard.tsx
import React, { useEffect } from 'react';
import { useSyncStore } from '@/stores/syncStore';
import { useSSE } from '@/hooks/useSSE';
import { StatsCards } from './StatsCards';
import { SyncButtons } from './SyncButtons';
import { ProgressBar } from './ProgressBar';

export const SyncDashboard: React.FC = () => {
  const { syncStatus, stats, fetchStats, isLoading } = useSyncStore();
  
  // 复用现有的 SSE 连接
  useSSE(syncStatus.taskId);

  // 组件加载时获取统计数据
  useEffect(() => {
    fetchStats();
  }, [fetchStats]);

  return (
    <div className="space-y-6">
      {/* 统计卡片 */}
      <StatsCards stats={stats} isLoading={isLoading} />
      
      {/* 同步进度条 */}
      {syncStatus.isActive && (
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">
            同步进度
          </h3>
          <ProgressBar
            progress={syncStatus.progress}
            status={syncStatus.status}
            currentStep={syncStatus.currentStep}
            errors={syncStatus.errors}
          />
        </div>
      )}
      
      {/* 同步操作按钮 */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">
          同步操作
        </h3>
        <SyncButtons disabled={syncStatus.isActive} />
      </div>
    </div>
  );
};
```

**验证标准**:
- [ ] 统计卡片正确显示数据
- [ ] 同步按钮可以触发 API 调用
- [ ] 进度条实时更新同步状态
- [ ] SSE 连接正常工作

---

## 📅 Week 3: 设置管理组件开发

### Task 3.1: 设置状态管理 (Day 1, 4小时)

**目标**: 实现设置相关的状态管理

**输出文件**:
- `src/stores/settingsStore.ts`
- `src/types/settings.ts`

**设置类型定义**:
```typescript
// src/types/settings.ts
export interface Settings {
  // API 设置
  apiKey: string;
  databaseId: string;
  syncSchedule: string;
  
  // 字段映射
  fieldMapping: {
    title: string;
    status: string;
    post_type: string;
    date: string;
    excerpt: string;
    featured_image: string;
    categories: string;
    tags: string;
    password: string;
  };
  
  // 自定义字段映射
  customFieldMapping: Array<{
    notion_property: string;
    wp_field: string;
    field_type: string;
  }>;
  
  // 性能配置
  performanceConfig: {
    api_page_size: number;
    concurrent_requests: number;
    batch_size: number;
    log_buffer_size: number;
    enable_performance_mode: boolean;
  };
  
  // Webhook 配置
  webhookConfig: {
    enabled: boolean;
    token: string;
    verification_token: string;
    incremental_sync: boolean;
    check_deletions: boolean;
  };
  
  // 其他设置
  debugLevel: string;
  maxImageSize: number;
  pluginLanguage: string;
  deleteOnUninstall: boolean;
}

export interface SettingsFormData extends Settings {}
```

**设置 Store 实现**:
```typescript
// src/stores/settingsStore.ts
import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { apiService } from '@/services/api';
import type { Settings } from '@/types/settings';

interface SettingsStore {
  settings: Settings;
  isLoading: boolean;
  isSaving: boolean;
  lastSaved: string | null;
  
  // Actions
  loadSettings: () => Promise<void>;
  saveSettings: (settings: Partial<Settings>) => Promise<void>;
  updateSettings: (updates: Partial<Settings>) => void;
  resetSettings: () => void;
  testConnection: (apiKey: string, databaseId: string) => Promise<boolean>;
}

const defaultSettings: Settings = {
  apiKey: '',
  databaseId: '',
  syncSchedule: 'manual',
  fieldMapping: {
    title: 'Title,标题',
    status: 'Status,状态',
    post_type: 'Type,类型',
    date: 'Date,日期',
    excerpt: 'Summary,摘要,Excerpt',
    featured_image: 'Featured Image,特色图片',
    categories: 'Categories,分类,Category',
    tags: 'Tags,标签,Tag',
    password: 'Password,密码',
  },
  customFieldMapping: [],
  performanceConfig: {
    api_page_size: 100,
    concurrent_requests: 5,
    batch_size: 20,
    log_buffer_size: 50,
    enable_performance_mode: true,
  },
  webhookConfig: {
    enabled: false,
    token: '',
    verification_token: '',
    incremental_sync: true,
    check_deletions: true,
  },
  debugLevel: 'error',
  maxImageSize: 5,
  pluginLanguage: 'auto',
  deleteOnUninstall: false,
};

export const useSettingsStore = create<SettingsStore>()(
  persist(
    (set, get) => ({
      settings: defaultSettings,
      isLoading: false,
      isSaving: false,
      lastSaved: null,

      loadSettings: async () => {
        set({ isLoading: true });
        try {
          const settings = await apiService.getSettings();
          set({ 
            settings: { ...defaultSettings, ...settings },
            lastSaved: new Date().toISOString(),
          });
        } catch (error) {
          console.error('Failed to load settings:', error);
        } finally {
          set({ isLoading: false });
        }
      },

      saveSettings: async (settingsUpdate) => {
        set({ isSaving: true });
        try {
          const currentSettings = get().settings;
          const newSettings = { ...currentSettings, ...settingsUpdate };
          
          await apiService.saveSettings(newSettings);
          
          set({ 
            settings: newSettings,
            lastSaved: new Date().toISOString(),
          });
          
          return true;
        } catch (error) {
          console.error('Failed to save settings:', error);
          throw error;
        } finally {
          set({ isSaving: false });
        }
      },

      updateSettings: (updates) => {
        set((state) => ({
          settings: { ...state.settings, ...updates },
        }));
      },

      resetSettings: () => {
        set({
          settings: defaultSettings,
          lastSaved: null,
        });
      },

      testConnection: async (apiKey, databaseId) => {
        try {
          return await apiService.testConnection(apiKey, databaseId);
        } catch (error) {
          console.error('Connection test failed:', error);
          return false;
        }
      },
    }),
    {
      name: 'notion-wp-settings',
      partialize: (state) => ({ 
        settings: state.settings, 
        lastSaved: state.lastSaved 
      }),
    }
  )
);
```

**验证标准**:
- [ ] 设置可以正确加载和保存
- [ ] 表单状态正确同步
- [ ] 连接测试功能正常

### Task 3.2: 表单组件开发 (Day 1-2, 6小时)

**目标**: 创建通用的表单组件

**输出文件**:
```
src/components/Common/
├── Form/
│   ├── FormField.tsx
│   ├── FormInput.tsx
│   ├── FormSelect.tsx
│   ├── FormTextarea.tsx
│   ├── FormCheckbox.tsx
│   └── index.ts
```

**表单字段组件**:
```typescript
// src/components/Common/Form/FormField.tsx
import React from 'react';
import { cn } from '@/utils/helpers';

interface FormFieldProps {
  label: string;
  error?: string;
  required?: boolean;
  description?: string;
  children: React.ReactNode;
  className?: string;
}

export const FormField: React.FC<FormFieldProps> = ({
  label,
  error,
  required = false,
  description,
  children,
  className,
}) => {
  return (
    <div className={cn('space-y-2', className)}>
      <label className="block text-sm font-medium text-gray-700">
        {label}
        {required && <span className="text-red-500 ml-1">*</span>}
      </label>
      
      {children}
      
      {description && (
        <p className="text-sm text-gray-500">{description}</p>
      )}
      
      {error && (
        <p className="text-sm text-red-600">{error}</p>
      )}
    </div>
  );
};
```

**输入框组件**:
```typescript
// src/components/Common/Form/FormInput.tsx
import React, { forwardRef } from 'react';
import { Eye, EyeOff } from 'lucide-react';
import { cn } from '@/utils/helpers';

interface FormInputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  error?: string;
  showPasswordToggle?: boolean;
}

export const FormInput = forwardRef<HTMLInputElement, FormInputProps>(
  ({ className, error, showPasswordToggle = false, type, ...props }, ref) => {
    const [showPassword, setShowPassword] = React.useState(false);
    
    const inputType = showPasswordToggle 
      ? (showPassword ? 'text' : 'password')
      : type;

    return (
      <div className="relative">
        <input
          type={inputType}
          className={cn(
            'flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:cursor-not-allowed disabled:opacity-50',
            error && 'border-red-500 focus:ring-red-500',
            showPasswordToggle && 'pr-10',
            className
          )}
          ref={ref}
          {...props}
        />
        
        {showPasswordToggle && (
          <button
            type="button"
            className="absolute inset-y-0 right-0 pr-3 flex items-center"
            onClick={() => setShowPassword(!showPassword)}
          >
            {showPassword ? (
              <EyeOff className="h-4 w-4 text-gray-400" />
            ) : (
              <Eye className="h-4 w-4 text-gray-400" />
            )}
          </button>
        )}
      </div>
    );
  }
);

FormInput.displayName = 'FormInput';
```

**验证标准**:
- [ ] 表单组件渲染正确
- [ ] 验证错误正确显示
- [ ] 表单提交正常工作

### Task 3.3: 设置面板组件 (Day 2-3, 8小时)

**目标**: 实现设置管理的主要界面

**输出文件**:
```
src/components/Settings/
├── SettingsPanel.tsx
├── ApiSettings.tsx
├── FieldMapping.tsx
├── PerformanceConfig.tsx
├── DebugSettings.tsx
└── index.ts
```

**API 设置组件**:
```typescript
// src/components/Settings/ApiSettings.tsx
import React from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'react-hot-toast';
import { FormField } from '@/components/Common/Form/FormField';
import { FormInput } from '@/components/Common/Form/FormInput';
import { FormSelect } from '@/components/Common/Form/FormSelect';
import { Button } from '@/components/Common/Button';
import { useSettingsStore } from '@/stores/settingsStore';

interface ApiSettingsProps {
  onTestConnection?: () => void;
}

export const ApiSettings: React.FC<ApiSettingsProps> = ({ onTestConnection }) => {
  const { settings, updateSettings, testConnection, isSaving } = useSettingsStore();
  
  const {
    register,
    handleSubmit,
    watch,
    formState: { errors, isDirty },
  } = useForm({
    defaultValues: {
      apiKey: settings.apiKey,
      databaseId: settings.databaseId,
      syncSchedule: settings.syncSchedule,
    },
  });

  const apiKey = watch('apiKey');
  const databaseId = watch('databaseId');

  const handleTestConnection = async () => {
    if (!apiKey || !databaseId) {
      toast.error('请先填写 API 密钥和数据库 ID');
      return;
    }

    try {
      const isConnected = await testConnection(apiKey, databaseId);
      if (isConnected) {
        toast.success('连接测试成功！');
      } else {
        toast.error('连接测试失败，请检查您的设置');
      }
    } catch (error) {
      toast.error('连接测试失败');
    }
  };

  const onSubmit = (data: any) => {
    updateSettings(data);
    toast.success('设置已更新');
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      <FormField
        label="API 密钥"
        required
        description="在 Notion 的"我的集成"页面创建并获取 API 密钥"
        error={errors.apiKey?.message}
      >
        <FormInput
          {...register('apiKey', { required: '请输入 API 密钥' })}
          type="password"
          showPasswordToggle
          placeholder="输入您的 Notion API 密钥"
          error={errors.apiKey?.message}
        />
      </FormField>

      <FormField
        label="数据库 ID"
        required
        description="可以从 Notion 数据库 URL 中找到"
        error={errors.databaseId?.message}
      >
        <FormInput
          {...register('databaseId', { required: '请输入数据库 ID' })}
          placeholder="输入您的 Notion 数据库 ID"
          error={errors.databaseId?.message}
        />
      </FormField>

      <FormField
        label="自动同步频率"
        description='选择 "手动同步" 以禁用定时任务'
      >
        <FormSelect
          {...register('syncSchedule')}
          options={[
            { value: 'manual', label: '手动同步' },
            { value: 'twicedaily', label: '每天两次' },
            { value: 'daily', label: '每天一次' },
            { value: 'weekly', label: '每周一次' },
            { value: 'monthly', label: '每月一次' },
          ]}
        />
      </FormField>

      <div className="flex items-center space-x-3">
        <Button
          type="button"
          variant="secondary"
          onClick={handleTestConnection}
          disabled={!apiKey || !databaseId}
        >
          测试连接
        </Button>
        
        <Button
          type="submit"
          disabled={!isDirty}
          loading={isSaving}
        >
          保存设置
        </Button>
      </div>
    </form>
  );
};
```

**验证标准**:
- [ ] 设置表单可以正确提交
- [ ] 连接测试功能正常
- [ ] 表单验证工作正确

---

## 📅 Week 4: 监控和调试工具

### Task 4.1: 性能监控组件 (Day 1-2, 6小时)

**目标**: 实现性能监控界面

**输出文件**:
```
src/components/Monitor/
├── PerformanceMonitor.tsx
├── SystemInfo.tsx
├── AsyncStatus.tsx
└── index.ts
```

**系统信息组件**:
```typescript
// src/components/Monitor/SystemInfo.tsx
import React, { useEffect, useState } from 'react';
import { Card } from '@/components/Common/Card';

interface SystemInfoProps {
  className?: string;
}

export const SystemInfo: React.FC<SystemInfoProps> = ({ className }) => {
  const [systemInfo, setSystemInfo] = useState({
    phpVersion: '',
    memoryLimit: '',
    currentMemory: '',
    peakMemory: '',
  });

  useEffect(() => {
    // 这些信息通常通过 API 获取
    setSystemInfo({
      phpVersion: window.wpNotionConfig?.systemInfo?.phpVersion || 'Unknown',
      memoryLimit: window.wpNotionConfig?.systemInfo?.memoryLimit || 'Unknown',
      currentMemory: window.wpNotionConfig?.systemInfo?.currentMemory || 'Unknown',
      peakMemory: window.wpNotionConfig?.systemInfo?.peakMemory || 'Unknown',
    });
  }, []);

  return (
    <Card title="系统信息" className={className}>
      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-3">
          <div>
            <span className="text-sm font-medium text-gray-600">PHP 版本:</span>
            <span className="ml-2 text-sm text-gray-900">{systemInfo.phpVersion}</span>
          </div>
          <div>
            <span className="text-sm font-medium text-gray-600">内存限制:</span>
            <span className="ml-2 text-sm text-gray-900">{systemInfo.memoryLimit}</span>
          </div>
        </div>
        <div className="space-y-3">
          <div>
            <span className="text-sm font-medium text-gray-600">当前内存:</span>
            <span className="ml-2 text-sm text-gray-900">{systemInfo.currentMemory}</span>
          </div>
          <div>
            <span className="text-sm font-medium text-gray-600">峰值内存:</span>
            <span className="ml-2 text-sm text-gray-900">{systemInfo.peakMemory}</span>
          </div>
        </div>
      </div>
    </Card>
  );
};
```

**验证标准**:
- [ ] 性能数据正确显示
- [ ] 图表组件渲染正常
- [ ] 数据刷新功能正常

### Task 4.2: 日志查看器 (Day 2-3, 6小时)

**目标**: 实现日志查看和管理功能

**输出文件**:
- `src/components/Monitor/LogViewer.tsx`

**日志查看器组件**:
```typescript
// src/components/Monitor/LogViewer.tsx
import React, { useState, useEffect } from 'react';
import { Trash2, Download, RefreshCw } from 'lucide-react';
import { Button } from '@/components/Common/Button';
import { FormSelect } from '@/components/Common/Form/FormSelect';
import { apiService } from '@/services/api';
import { toast } from 'react-hot-toast';

export const LogViewer: React.FC = () => {
  const [logs, setLogs] = useState<string>('');
  const [selectedFile, setSelectedFile] = useState<string>('');
  const [logFiles, setLogFiles] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    fetchLogFiles();
  }, []);

  const fetchLogFiles = async () => {
    try {
      const files = await apiService.getLogFiles();
      setLogFiles(files);
      if (files.length > 0 && !selectedFile) {
        setSelectedFile(files[0]);
      }
    } catch (error) {
      console.error('Failed to fetch log files:', error);
    }
  };

  const fetchLogs = async () => {
    if (!selectedFile) return;
    
    setIsLoading(true);
    try {
      const logContent = await apiService.getLogs(selectedFile);
      setLogs(logContent);
    } catch (error) {
      toast.error('获取日志失败');
    } finally {
      setIsLoading(false);
    }
  };

  const clearLogs = async () => {
    try {
      await apiService.clearLogs();
      setLogs('');
      toast.success('日志已清除');
    } catch (error) {
      toast.error('清除日志失败');
    }
  };

  const downloadLogs = () => {
    const blob = new Blob([logs], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = selectedFile || 'logs.txt';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <FormSelect
            value={selectedFile}
            onChange={(e) => setSelectedFile(e.target.value)}
            options={logFiles.map(file => ({ value: file, label: file }))}
            className="w-48"
          />
          <Button
            onClick={fetchLogs}
            disabled={!selectedFile || isLoading}
            loading={isLoading}
            size="sm"
          >
            <RefreshCw className="w-4 h-4 mr-1" />
            查看日志
          </Button>
        </div>
        
        <div className="flex items-center space-x-2">
          <Button
            onClick={downloadLogs}
            disabled={!logs}
            variant="secondary"
            size="sm"
          >
            <Download className="w-4 h-4 mr-1" />
            下载
          </Button>
          <Button
            onClick={clearLogs}
            variant="danger"
            size="sm"
          >
            <Trash2 className="w-4 h-4 mr-1" />
            清除
          </Button>
        </div>
      </div>

      <div className="bg-gray-900 text-green-400 rounded-lg p-4 font-mono text-sm overflow-auto max-h-96">
        {logs ? (
          <pre className="whitespace-pre-wrap">{logs}</pre>
        ) : (
          <div className="text-gray-500 text-center py-8">
            选择日志文件并点击"查看日志"
          </div>
        )}
      </div>
    </div>
  );
};
```

**验证标准**:
- [ ] 日志文件可以正确加载
- [ ] 日志内容正确显示
- [ ] 清除和下载功能正常

---

## 📅 Week 5: 优化和部署

### Task 5.1: 布局和导航 (Day 1, 4小时)

**目标**: 实现完整的布局系统

**输出文件**:
```
src/components/Layout/
├── AdminLayout.tsx
├── Sidebar.tsx
├── TabContent.tsx
└── index.ts
```

**主布局组件**:
```typescript
// src/components/Layout/AdminLayout.tsx
import React, { useState } from 'react';
import { Sidebar } from './Sidebar';
import { TabContent } from './TabContent';

export const AdminLayout: React.FC = () => {
  const [activeTab, setActiveTab] = useState('sync');

  return (
    <div className="flex min-h-screen bg-gray-50">
      <Sidebar activeTab={activeTab} onTabChange={setActiveTab} />
      <main className="flex-1 p-6">
        <div className="max-w-7xl mx-auto">
          <TabContent activeTab={activeTab} />
        </div>
      </main>
    </div>
  );
};
```

**验证标准**:
- [ ] 布局结构正确
- [ ] 导航切换正常
- [ ] 响应式设计正常

### Task 5.2: 性能优化 (Day 1-2, 6小时)

**目标**: 优化应用性能

**优化项目**:
1. **代码分割**: 实现路由级别的懒加载
2. **缓存策略**: 配置适当的缓存策略
3. **包体积优化**: 移除未使用的代码

**代码分割实现**:
```typescript
// src/components/Layout/TabContent.tsx
import React, { Suspense, lazy } from 'react';
import { Loading } from '@/components/Common/Loading';

// 懒加载组件
const SyncDashboard = lazy(() => import('@/components/Dashboard/SyncDashboard'));
const SettingsPanel = lazy(() => import('@/components/Settings/SettingsPanel'));
const PerformanceMonitor = lazy(() => import('@/components/Monitor/PerformanceMonitor'));

interface TabContentProps {
  activeTab: string;
}

export const TabContent: React.FC<TabContentProps> = ({ activeTab }) => {
  return (
    <Suspense fallback={<Loading />}>
      {activeTab === 'sync' && <SyncDashboard />}
      {activeTab === 'settings' && <SettingsPanel />}
      {activeTab === 'monitor' && <PerformanceMonitor />}
    </Suspense>
  );
};
```

**验证标准**:
- [ ] 应用加载速度符合要求
- [ ] 包体积在合理范围内
- [ ] 用户体验流畅

### Task 5.3: 生产构建和部署 (Day 2-3, 6小时)

**目标**: 配置生产环境构建

**构建配置优化**:
```typescript
// frontend/vite.config.ts
import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import path from 'path';

export default defineConfig({
  plugins: [react()],
  
  build: {
    outDir: '../assets/dist',
    emptyOutDir: true,
    minify: 'terser',
    rollupOptions: {
      input: './src/main.tsx',
      output: {
        entryFileNames: 'js/admin-react.[hash].js',
        chunkFileNames: 'js/chunk.[hash].js',
        assetFileNames: 'css/admin-react.[hash].css',
        manualChunks: {
          vendor: ['react', 'react-dom'],
          utils: ['axios', 'zustand'],
        },
      },
    },
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true,
      },
    },
  },
  
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
});
```

**PHP 集成最终版本**:
```php
// 在 WordPress 管理页面中集成 React
private function enqueue_react_app() {
    $manifest_path = plugin_dir_path(__FILE__) . '../assets/dist/manifest.json';
    
    if (!file_exists($manifest_path)) {
        // 开发模式：加载开发服务器
        if (defined('WP_DEBUG') && WP_DEBUG) {
            wp_enqueue_script(
                'notion-wp-react-dev',
                'http://localhost:3000/src/main.tsx',
                [],
                NOTION_TO_WORDPRESS_VERSION,
                true
            );
        }
        return;
    }
    
    // 生产模式：加载构建文件
    $manifest = json_decode(file_get_contents($manifest_path), true);
    
    if (isset($manifest['src/main.tsx'])) {
        $main_file = $manifest['src/main.tsx'];
        
        // 加载主 JS 文件
        wp_enqueue_script(
            'notion-wp-react',
            plugin_dir_url(__FILE__) . '../assets/dist/' . $main_file['file'],
            [],
            NOTION_TO_WORDPRESS_VERSION,
            true
        );
        
        // 加载 CSS 文件
        if (isset($main_file['css'])) {
            foreach ($main_file['css'] as $css_file) {
                wp_enqueue_style(
                    'notion-wp-react-style',
                    plugin_dir_url(__FILE__) . '../assets/dist/' . $css_file,
                    [],
                    NOTION_TO_WORDPRESS_VERSION
                );
            }
        }
        
        // 传递配置给 React
        wp_localize_script('notion-wp-react', 'wpNotionConfig', [
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('notion_wp_nonce'),
            'apiUrl' => home_url('/wp-json/notion-to-wordpress/v1/'),
            'systemInfo' => [
                'phpVersion' => PHP_VERSION,
                'memoryLimit' => ini_get('memory_limit'),
                'currentMemory' => size_format(memory_get_usage(true)),
                'peakMemory' => size_format(memory_get_peak_usage(true)),
            ],
        ]);
    }
}
```

**验证标准**:
- [ ] 生产构建成功完成
- [ ] 资源正确加载到 WordPress
- [ ] 所有功能在生产环境正常工作

---

## 🔍 最终验收标准

### 功能完整性
- [ ] 同步功能完全正常（智能同步、完全同步）
- [ ] 实时进度显示正确（SSE 连接）
- [ ] 设置保存和加载正常
- [ ] 连接测试功能正常
- [ ] 性能监控数据正确
- [ ] 日志查看功能正常

### 技术指标
- [ ] TypeScript 编译无错误
- [ ] 应用包大小 < 500KB (gzipped)
- [ ] 初始加载时间 < 3 秒
- [ ] 页面切换响应时间 < 500ms

### 兼容性
- [ ] 现有 PHP 功能完全保持
- [ ] 所有 AJAX 接口正常工作
- [ ] SSE 连接稳定
- [ ] 多浏览器兼容（Chrome, Firefox, Safari, Edge）

### 部署就绪
- [ ] 生产构建配置正确
- [ ] 资源加载策略正确
- [ ] 错误处理完善
- [ ] 回滚方案可用

---

## 🛠️ AI Vibe Coding 提示词模板

### 组件开发提示词
```
请帮我实现一个 React 组件：{组件名称}

要求：
1. 使用 TypeScript
2. 遵循现有的代码风格
3. 包含适当的错误处理
4. 添加必要的 prop 类型定义
5. 实现响应式设计

参考现有组件：{参考组件路径}
需要集成的 API：{API 接口}
设计要求：{UI/UX 要求}

请提供完整的组件代码和使用示例。
```

### API 集成提示词
```
请帮我为现有的 WordPress AJAX 接口创建 React 集成：

现有 PHP 接口：
- 端点：{action 名称}
- 参数：{参数列表}
- 返回格式：{返回格式}

需要实现：
1. TypeScript 类型定义
2. API 服务方法
3. React Hook (如需要)
4. 错误处理和状态管理

请保持与现有 PHP 接口的完全兼容。
```

### 状态管理提示词
```
请帮我使用 Zustand 实现状态管理：

状态需求：
- 状态字段：{状态字段列表}
- 操作方法：{方法列表}
- 持久化需求：{是否需要持久化}

需要与以下 API 集成：{API 列表}

请提供完整的 store 实现和使用示例。
```

---

这个详细的任务拆分方案为 AI Vibe Coding 提供了：

1. **明确的输入输出**: 每个任务都有清晰的文件要求
2. **具体的代码模板**: 提供了可直接使用的代码框架
3. **详细的验证标准**: 每个步骤都有明确的完成标准
4. **渐进式实施**: 可以分步骤独立验证和部署
5. **风险控制**: 每个阶段都可以回滚
6. **AI 友好**: 提供了具体的提示词模板

这样的方案确保了重构过程的可控性和成功率。