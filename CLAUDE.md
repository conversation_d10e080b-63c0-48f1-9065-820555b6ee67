# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Build and Development Commands

### Primary Development Workflow
```bash
# Check version consistency before development
npm run version:check

# Build production package
npm run build

# Clean build directory
npm run build:clean

# Verify build results
npm run build:verify
```

### Version Management
```bash
# Bump versions (automatically updates all version files)
npm run version:patch    # 1.0.0 → 1.0.1
npm run version:minor    # 1.0.0 → 1.1.0
npm run version:major    # 1.0.0 → 2.0.0
npm run version:beta     # 1.0.0 → 1.0.1-beta.1

# Set custom version (use node directly)
node scripts/version-bump.js --version=X.Y.Z
```

### Release Management
```bash
# Create releases (automatically creates GitHub release)
npm run release:patch
npm run release:minor
npm run release:major
npm run release:beta

# Preview release (dry run mode)
npm run release:dry-run
```

### Testing and Validation
```bash
# Run integration tests
npm run test

# Validate configuration and environment
npm run validate
npm run validate:config
npm run validate:github-actions
```

### PHP Commands
```bash
# PHP code standards checking
composer run cs-check

# Auto-fix PHP code standards
composer run cs-fix

# Static analysis
composer run analyze

# Run PHP unit tests
composer test
```

### Frontend Development (React)
```bash
cd frontend

# Development server
npm run dev

# Type checking
npm run type-check

# Build frontend
npm run build

# Lint TypeScript/React code
npm run lint
```

## Architecture Overview

### Plugin Structure
This is a WordPress plugin that syncs content from Notion databases to WordPress posts. The architecture follows a layered design pattern:

- **Framework Layer** (`includes/framework/`): Core plugin initialization and WordPress integration
- **Services Layer** (`includes/services/`): Business logic for API communication, content conversion, and sync management
- **Handlers Layer** (`includes/handlers/`): Coordinators for import operations, webhooks, and integrations
- **Core Layer** (`includes/core/`): Infrastructure services like logging, security, HTTP client, performance monitoring
- **Utils Layer** (`includes/utils/`): Helper utilities and support functions
- **API Layer** (`includes/api/`): SSE progress streaming and API interfaces

### Key Components

#### Sync Engine
- **Import Coordinator** (`includes/handlers/Import_Coordinator.php`): Main orchestrator for sync operations
- **Sync Manager** (`includes/services/Sync_Manager.php`): Manages different sync modes (manual, scheduled, webhook)
- **Content Converter** (`includes/services/Content_Converter.php`): Transforms Notion blocks to WordPress content

#### API Communication
- **Notion API** (`includes/services/API.php`): Handles all Notion API interactions with retry logic and rate limiting
- **HTTP Client** (`includes/core/HTTP_Client.php`): Unified HTTP client with error handling

#### Performance & Reliability
- **Modern Async Engine** (`includes/core/Modern_Async_Engine.php`): Asynchronous task processing
- **Performance Monitor** (`includes/core/Performance_Monitor.php`): Tracks sync performance metrics
- **Progress Tracker** (`includes/core/Progress_Tracker.php`): Real-time progress tracking with SSE
- **Error Handler** (`includes/core/Error_Handler.php`): Centralized error handling and recovery

### Frontend Architecture
- **React + TypeScript** frontend built with Vite
- **Zustand** for state management
- **Tailwind CSS** for styling
- Real-time progress updates via Server-Sent Events (SSE)

### Sync Modes
1. **Manual Sync**: On-demand sync triggered by admin
2. **Scheduled Sync**: WordPress cron-based automated sync
3. **Webhook Sync**: Real-time sync triggered by Notion events

### Data Flow
```
Notion API → API Communication → Content Conversion → WordPress Database
     ↑                                                        ↑
  Webhook Handler                                      Admin Interface
```

## Development Guidelines

### Code Standards
- Follow PSR-4 autoloading standards (already configured in composer.json)
- Use WordPress coding standards for PHP
- Use namespace `NTWP\` for all classes
- Always include PHPDoc comments
- Use unified error handling via `\NTWP\Core\Error_Handler`

### Security Practices
- All user input must be validated via `\NTWP\Core\Security::validate_*()` methods
- Use WordPress nonce verification for AJAX requests
- Escape all output using WordPress functions (`esc_html`, `esc_url`, etc.)
- API keys stored securely in WordPress options

### Testing
- No PHPUnit configuration found - tests should be run manually via WordPress test environment
- Frontend tests run via npm scripts in the `frontend/` directory
- Integration tests available via `npm run test:integration`

### Version Management
- Version numbers must be consistent across all files
- Use semantic versioning (major.minor.patch)
- Always run `npm run version:check` before development
- Version files: `package.json`, `notion-to-wordpress.php`, `frontend/package.json`

### Performance Considerations
- Plugin uses real-time database queries for data consistency
- Incremental sync only processes changed content
- Batch operations for large datasets
- Memory management for processing large Notion databases
- Performance mode constant `NOTION_PERFORMANCE_MODE` reduces logging overhead

## Important Notes

### Database Operations
- Plugin creates custom tables for sync metadata and progress tracking
- Uses WordPress built-in post types for storing synced content
- Implements intelligent deletion detection for removed Notion pages

### Error Handling
- Centralized error handling via `\NTWP\Core\Error_Handler`
- Comprehensive logging system with different severity levels
- Automatic retry mechanisms for API failures
- Graceful degradation for non-critical errors

### Internationalization
- Plugin supports English and Simplified Chinese
- Translation files in `languages/` directory
- Use WordPress i18n functions for new strings

### File Structure Important Details
- Main plugin file: `notion-to-wordpress.php`
- Admin interface: `admin/` directory with modern CSS and JavaScript
- Frontend React app: `frontend/` directory (separate build process)
- Build scripts: `scripts/` directory for automation
- Documentation: `docs/` directory with comprehensive guides

This plugin is actively maintained with automated release management and comprehensive error handling. Always test changes thoroughly in a development environment before deployment.